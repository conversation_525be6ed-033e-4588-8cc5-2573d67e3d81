steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/facetracepro/facetrace-gov', '.']
  
  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/facetracepro/facetrace-gov']
  
  # Deploy container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
    - 'run'
    - 'deploy'
    - 'facetrace-gov'
    - '--image'
    - 'gcr.io/facetracepro/facetrace-gov'
    - '--region'
    - 'us-central1'
    - '--platform'
    - 'managed'
    - '--allow-unauthenticated'
    - '--memory'
    - '2Gi'
    - '--cpu'
    - '2'
    - '--timeout'
    - '3600'
    - '--concurrency'
    - '1000'
    - '--max-instances'
    - '10'
    - '--min-instances'
    - '0'
    - '--set-env-vars'
    - 'NODE_ENV=production,NEXT_PUBLIC_DISABLE_AUTH=true,NEXT_PUBLIC_DISABLE_PAYMENT=true,GOV_PORTAL=true'
    - '--set-env-vars'
    - 'DATABASE_URL=postgresql://facetracepro_owner:<EMAIL>/facetracepro?sslmode=require'

images:
  - gcr.io/facetracepro/facetrace-gov
