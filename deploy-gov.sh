#!/bin/bash

# FaceTrace Government Portal Deployment Script for Google Cloud Run
# This script deploys the government version with subdomain support

set -e

# Configuration
PROJECT_ID="facetracepro"
SERVICE_NAME="facetrace-gov"
REGION="us-central1"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

echo "🏛️ Starting FaceTrace Government Portal deployment to Google Cloud Run..."

# Step 1: Authenticate and set project
echo "📋 Step 1: Setting up Google Cloud authentication..."
echo "🔍 Current project: $(gcloud config get-value project 2>/dev/null || echo 'Not set')"
echo "🔍 Current account: $(gcloud config get-value account 2>/dev/null || echo 'Not logged in')"

# Check if already authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "🔐 Please authenticate with Google Cloud..."
    gcloud auth login
else
    echo "✅ Already authenticated"
fi

gcloud config set project $PROJECT_ID
echo "✅ Project set to: $PROJECT_ID"

echo "🔧 Enabling required APIs..."
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Step 2: Run database migration for government users
echo "🗄️ Step 2: Running government users database migration..."
if command -v npm &> /dev/null; then
    echo "📦 Running migration with npm..."
    npm run db:migrate:gov || {
        echo "⚠️ Migration failed, but continuing with deployment..."
        echo "📝 You may need to run the migration manually later"
    }
else
    echo "⚠️ npm not found, skipping migration"
    echo "📝 Please run 'npm run db:migrate:gov' manually after deployment"
fi

# Step 3: Build and push Docker image
echo "🐳 Step 3: Building Docker image for government portal..."
if [ ! -f "Dockerfile" ]; then
    echo "❌ Error: Dockerfile not found!"
    echo "📝 Please ensure Dockerfile is in the current directory"
    exit 1
fi

echo "🔨 Building Docker image..."
docker build -t $IMAGE_NAME . || {
    echo "❌ Docker build failed!"
    echo "📝 Please check your Docker installation and try again"
    exit 1
}

echo "📤 Pushing image to Google Container Registry..."
docker push $IMAGE_NAME || {
    echo "❌ Docker push failed!"
    echo "📝 Please check your authentication and try again"
    exit 1
}

# Step 4: Deploy to Cloud Run
echo "☁️ Step 4: Deploying government portal to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
  --image $IMAGE_NAME \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --timeout 3600 \
  --concurrency 1000 \
  --max-instances 10 \
  --min-instances 0 \
  --project $PROJECT_ID \
  --set-env-vars NODE_ENV=production,NEXT_PUBLIC_DISABLE_AUTH=true,NEXT_PUBLIC_DISABLE_PAYMENT=true,GOV_PORTAL=true || {
    echo "❌ Cloud Run deployment failed!"
    echo "📝 Please check the error messages above and try again"
    exit 1
}

echo "✅ Government portal deployment completed!"
echo "🌐 Your government portal is available at:"
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)')
echo $SERVICE_URL

echo ""
echo "📝 Next steps:"
echo "1. Set up custom domain mapping for gov.facetrace.com"
echo "2. Configure DNS to point to the Cloud Run service"
echo "3. Test the government login at: $SERVICE_URL/gov"
echo "4. Verify database migration completed successfully"
echo ""
echo "🔧 Government portal endpoints:"
echo "Login: $SERVICE_URL/gov"
echo "Search: $SERVICE_URL/gov/search"
echo "Auth API: $SERVICE_URL/api/gov/auth"
echo ""
echo "🏛️ Government Portal Ready!"
echo "📧 Test credentials:"
echo "   Entity: test"
echo "   Credential: <EMAIL>"
echo "   Access Code: brycebayens123"
