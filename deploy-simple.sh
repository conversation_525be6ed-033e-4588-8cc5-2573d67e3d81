#!/bin/bash

# FaceTrace Simple Deployment using Google Cloud Buildpacks
# This script deploys without <PERSON><PERSON> using Cloud Buildpacks

set -e

# Configuration
PROJECT_ID="facetracepro"
REGION="us-central1"
SERVICE_NAME="facetrace-app"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "\n${CYAN}🚀 $1${NC}"
}

echo "🌐 FaceTrace Simple Deployment to Google Cloud Run"
echo "=================================================="
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo "Service: $SERVICE_NAME"
echo ""

# Step 1: Authentication and project setup
log_step "Step 1: Authentication and Project Setup"

if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    log_error "Not authenticated with Google Cloud"
    log_info "Please run: gcloud auth login"
    exit 1
fi

CURRENT_ACCOUNT=$(gcloud auth list --filter=status:ACTIVE --format="value(account)")
log_success "Authenticated as: $CURRENT_ACCOUNT"

gcloud config set project $PROJECT_ID
log_success "Project set to: $PROJECT_ID"

# Step 2: Enable required APIs
log_step "Step 2: Enabling Required APIs"

APIS=(
    "run.googleapis.com"
    "cloudbuild.googleapis.com"
    "artifactregistry.googleapis.com"
    "dns.googleapis.com"
    "domains.googleapis.com"
)

for api in "${APIS[@]}"; do
    log_info "Enabling $api..."
    gcloud services enable $api --project=$PROJECT_ID
done

log_success "All required APIs enabled"

# Step 3: Remove Dockerfile to force buildpacks
log_step "Step 3: Preparing for Buildpacks Deployment"

if [ -f "Dockerfile" ]; then
    log_info "Temporarily moving Dockerfile to use buildpacks..."
    mv Dockerfile Dockerfile.backup
fi

log_success "Ready for buildpacks deployment"

# Step 4: Deploy using buildpacks
log_step "Step 4: Deploying with Google Cloud Buildpacks"

log_info "Deploying FaceTrace application using buildpacks..."
gcloud run deploy $SERVICE_NAME \
    --source . \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --memory 2Gi \
    --cpu 2 \
    --timeout 3600 \
    --concurrency 1000 \
    --max-instances 10 \
    --min-instances 0 \
    --set-env-vars "NODE_ENV=production" \
    --set-env-vars "NEXT_PUBLIC_DISABLE_AUTH=true" \
    --set-env-vars "NEXT_PUBLIC_DISABLE_PAYMENT=true" \
    --set-env-vars "DISABLE_AUTH=true" \
    --set-env-vars "DISABLE_PAYMENT=true" \
    --set-env-vars "DATABASE_URL=postgresql://facetracepro_owner:<EMAIL>/facetracepro?sslmode=require" \
    --project $PROJECT_ID

# Step 5: Restore Dockerfile if it existed
if [ -f "Dockerfile.backup" ]; then
    log_info "Restoring Dockerfile..."
    mv Dockerfile.backup Dockerfile
fi

# Step 6: Get service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --project $PROJECT_ID --format 'value(status.url)' 2>/dev/null || echo "")
if [[ -n "$SERVICE_URL" ]]; then
    log_success "Application deployed at: $SERVICE_URL"
else
    log_warning "Could not retrieve service URL"
fi

# Step 7: Set up domain mappings
log_step "Step 5: Setting Up Domain Mappings"

log_info "Setting up domain mapping for main application..."
gcloud run domain-mappings create \
    --service $SERVICE_NAME \
    --domain facetrace.pro \
    --region $REGION \
    --project $PROJECT_ID || log_warning "Main domain mapping may already exist"

log_info "Setting up domain mapping for government portal..."
gcloud run domain-mappings create \
    --service $SERVICE_NAME \
    --domain gov.facetrace.pro \
    --region $REGION \
    --project $PROJECT_ID || log_warning "Government domain mapping may already exist"

log_success "Domain mappings configured"

# Step 8: Run database migration
log_step "Step 6: Running Database Migration"

log_info "Running government users database migration..."
npm run db:migrate:gov || log_warning "Migration may have already been run"

log_success "Database migration completed"

# Step 9: Test deployment
log_step "Step 7: Testing Deployment"

if [[ -n "$SERVICE_URL" ]]; then
    log_info "Testing main application..."
    curl -s "$SERVICE_URL" > /dev/null && log_success "Main application accessible" || log_warning "Main application test failed"
    
    log_info "Testing government portal..."
    curl -s "$SERVICE_URL/gov" > /dev/null && log_success "Government portal accessible" || log_warning "Government portal test failed"
fi

# Step 10: Display results
log_step "Step 8: Deployment Summary"

echo ""
echo "🎉 FaceTrace Simple Deployment Completed!"
echo ""
echo "📋 Deployment Summary:"
echo "   Project ID: $PROJECT_ID"
echo "   Region: $REGION"
echo "   Service: $SERVICE_NAME"
echo ""
echo "🌐 Service URL:"
if [[ -n "$SERVICE_URL" ]]; then
    echo "   Application: $SERVICE_URL"
fi
echo ""
echo "🔗 Custom Domain URLs (DNS already configured):"
echo "   Main Site: https://facetrace.pro"
echo "   Government Portal: https://gov.facetrace.pro"
echo ""
echo "🧪 Test URLs:"
if [[ -n "$SERVICE_URL" ]]; then
    echo "   Main App: $SERVICE_URL"
    echo "   Government Login: $SERVICE_URL/gov"
    echo "   Government Search: $SERVICE_URL/gov/search"
fi
echo ""
echo "📝 Next Steps:"
echo "   1. ✅ DNS is already configured"
echo "   2. ⏱️  Wait for SSL certificates to provision (15-30 minutes)"
echo "   3. 🧪 Test custom domains once SSL is ready"
echo ""
echo "🔧 Useful Commands:"
echo "   View services: gcloud run services list --region $REGION --project $PROJECT_ID"
echo "   View domain mappings: gcloud run domain-mappings list --region $REGION --project $PROJECT_ID"
echo "   View logs: gcloud run services logs read $SERVICE_NAME --region $REGION --project $PROJECT_ID"
echo ""
echo "✅ Simple deployment completed successfully!"
