#!/bin/bash

# FaceTrace Clean Deployment using gcloud CLI
# This script deploys directly from source using gcloud run deploy --source

set -e

# Configuration
PROJECT_ID="facetracepro"
REGION="us-central1"
MAIN_SERVICE="facetrace-main"
GOV_SERVICE="facetrace-gov"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "\n${CYAN}🚀 $1${NC}"
}

echo "🌐 FaceTrace Clean Deployment to Google Cloud Run"
echo "================================================="
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo "Main Service: $MAIN_SERVICE"
echo "Government Service: $GOV_SERVICE"
echo ""

# Step 1: Authentication and project setup
log_step "Step 1: Authentication and Project Setup"

if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    log_error "Not authenticated with Google Cloud"
    log_info "Please run: gcloud auth login"
    exit 1
fi

CURRENT_ACCOUNT=$(gcloud auth list --filter=status:ACTIVE --format="value(account)")
log_success "Authenticated as: $CURRENT_ACCOUNT"

gcloud config set project $PROJECT_ID
log_success "Project set to: $PROJECT_ID"

# Step 2: Enable required APIs
log_step "Step 2: Enabling Required APIs"

APIS=(
    "run.googleapis.com"
    "cloudbuild.googleapis.com"
    "containerregistry.googleapis.com"
    "dns.googleapis.com"
    "domains.googleapis.com"
    "artifactregistry.googleapis.com"
)

for api in "${APIS[@]}"; do
    log_info "Enabling $api..."
    gcloud services enable $api --project=$PROJECT_ID
done

log_success "All required APIs enabled"

# Step 3: Clean install dependencies
log_step "Step 3: Clean Install Dependencies"

log_info "Installing fresh dependencies..."
npm install

log_success "Dependencies installed"

# Step 4: Deploy main application using source
log_step "Step 4: Deploying Main Application from Source"

log_info "Deploying main FaceTrace application..."
gcloud run deploy $MAIN_SERVICE \
    --source . \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --memory 2Gi \
    --cpu 2 \
    --timeout 3600 \
    --concurrency 1000 \
    --max-instances 10 \
    --min-instances 0 \
    --set-env-vars "NODE_ENV=production" \
    --set-env-vars "NEXT_PUBLIC_DISABLE_AUTH=true" \
    --set-env-vars "NEXT_PUBLIC_DISABLE_PAYMENT=true" \
    --set-env-vars "DISABLE_AUTH=true" \
    --set-env-vars "DISABLE_PAYMENT=true" \
    --set-env-vars "DATABASE_URL=postgresql://facetracepro_owner:<EMAIL>/facetracepro?sslmode=require" \
    --project $PROJECT_ID

MAIN_URL=$(gcloud run services describe $MAIN_SERVICE --region $REGION --project $PROJECT_ID --format 'value(status.url)' 2>/dev/null || echo "")
if [[ -n "$MAIN_URL" ]]; then
    log_success "Main application deployed at: $MAIN_URL"
else
    log_warning "Could not retrieve main application URL"
fi

# Step 5: Deploy government portal
log_step "Step 5: Deploying Government Portal from Source"

log_info "Deploying government portal..."
gcloud run deploy $GOV_SERVICE \
    --source . \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --memory 2Gi \
    --cpu 2 \
    --timeout 3600 \
    --concurrency 1000 \
    --max-instances 10 \
    --min-instances 0 \
    --set-env-vars "NODE_ENV=production" \
    --set-env-vars "NEXT_PUBLIC_DISABLE_AUTH=true" \
    --set-env-vars "NEXT_PUBLIC_DISABLE_PAYMENT=true" \
    --set-env-vars "DISABLE_AUTH=true" \
    --set-env-vars "DISABLE_PAYMENT=true" \
    --set-env-vars "GOV_PORTAL=true" \
    --set-env-vars "DATABASE_URL=postgresql://facetracepro_owner:<EMAIL>/facetracepro?sslmode=require" \
    --project $PROJECT_ID

GOV_URL=$(gcloud run services describe $GOV_SERVICE --region $REGION --project $PROJECT_ID --format 'value(status.url)' 2>/dev/null || echo "")
if [[ -n "$GOV_URL" ]]; then
    log_success "Government portal deployed at: $GOV_URL"
else
    log_warning "Could not retrieve government portal URL"
fi

# Step 6: Set up domain mappings
log_step "Step 6: Setting Up Domain Mappings"

log_info "Setting up domain mapping for main application..."
gcloud run domain-mappings create \
    --service $MAIN_SERVICE \
    --domain facetrace.pro \
    --region $REGION \
    --project $PROJECT_ID || log_warning "Main domain mapping may already exist"

log_info "Setting up domain mapping for government portal..."
gcloud run domain-mappings create \
    --service $GOV_SERVICE \
    --domain gov.facetrace.pro \
    --region $REGION \
    --project $PROJECT_ID || log_warning "Government domain mapping may already exist"

log_success "Domain mappings configured"

# Step 7: Run database migration
log_step "Step 7: Running Database Migration"

log_info "Running government users database migration..."
npm run db:migrate:gov || log_warning "Migration may have already been run"

log_success "Database migration completed"

# Step 8: Test deployments
log_step "Step 8: Testing Deployments"

if [[ -n "$MAIN_URL" ]]; then
    log_info "Testing main application..."
    curl -s "$MAIN_URL" > /dev/null && log_success "Main application accessible" || log_warning "Main application test failed"
fi

if [[ -n "$GOV_URL" ]]; then
    log_info "Testing government portal..."
    curl -s "$GOV_URL/gov" > /dev/null && log_success "Government portal accessible" || log_warning "Government portal test failed"
fi

# Step 9: Display results
log_step "Step 9: Deployment Summary"

echo ""
echo "🎉 FaceTrace Clean Deployment Completed!"
echo ""
echo "📋 Deployment Summary:"
echo "   Project ID: $PROJECT_ID"
echo "   Region: $REGION"
echo ""
echo "🌐 Service URLs:"
if [[ -n "$MAIN_URL" ]]; then
    echo "   Main Application: $MAIN_URL"
fi
if [[ -n "$GOV_URL" ]]; then
    echo "   Government Portal: $GOV_URL"
fi
echo ""
echo "🔗 Custom Domain URLs (DNS already configured):"
echo "   Main Site: https://facetrace.pro"
echo "   Government Portal: https://gov.facetrace.pro"
echo ""
echo "🧪 Test URLs:"
if [[ -n "$MAIN_URL" ]]; then
    echo "   Main App: $MAIN_URL"
fi
if [[ -n "$GOV_URL" ]]; then
    echo "   Government Login: $GOV_URL/gov"
    echo "   Government Search: $GOV_URL/gov/search"
fi
echo ""
echo "📝 Next Steps:"
echo "   1. ✅ DNS is already configured"
echo "   2. ⏱️  Wait for SSL certificates to provision (15-30 minutes)"
echo "   3. 🧪 Test custom domains once SSL is ready"
echo "   4. 🔧 Configure additional environment variables if needed"
echo ""
echo "🔧 Useful Commands:"
echo "   View services: gcloud run services list --region $REGION --project $PROJECT_ID"
echo "   View domain mappings: gcloud run domain-mappings list --region $REGION --project $PROJECT_ID"
echo "   View logs: gcloud run services logs read $MAIN_SERVICE --region $REGION --project $PROJECT_ID"
echo ""
echo "✅ Clean deployment completed successfully!"
