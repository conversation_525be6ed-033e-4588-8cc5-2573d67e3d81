'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';

export default function GovSearchPage() {
  const router = useRouter();
  const [sessionData, setSessionData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for government session
    const govSession = localStorage.getItem('govSession');
    
    if (!govSession) {
      // Redirect to login if no session
      router.push('/gov');
      return;
    }

    try {
      const session = JSON.parse(govSession);
      setSessionData(session);
    } catch (error) {
      console.error('Invalid session data:', error);
      router.push('/gov');
      return;
    }

    setIsLoading(false);
  }, [router]);

  const handleLogout = () => {
    localStorage.removeItem('govSession');
    router.push('/gov');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Header */}
      <header className="bg-black/20 backdrop-blur-lg border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            {/* Logo */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
                <div className="text-white font-bold text-sm">FT</div>
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">
                  FaceTrace
                  <span className="bg-gradient-to-r from-blue-400 to-red-500 bg-clip-text text-transparent ml-1">
                    Gov
                  </span>
                </h1>
                <p className="text-xs text-blue-200">Government Portal</p>
              </div>
            </div>

            {/* User info and logout */}
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm text-white font-medium">{sessionData?.entity}</p>
                <p className="text-xs text-blue-200">{sessionData?.credential}</p>
              </div>
              <button
                onClick={handleLogout}
                className="px-4 py-2 bg-red-500/20 hover:bg-red-500/30 text-red-200 rounded-lg transition-colors text-sm"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center"
        >
          {/* Welcome message */}
          <div className="mb-12">
            <h2 className="text-4xl font-bold text-white mb-4">
              Welcome to FaceTrace Government Portal
            </h2>
            <p className="text-xl text-blue-200 mb-2">
              Authorized access for {sessionData?.entity}
            </p>
            <p className="text-blue-300">
              Logged in as: {sessionData?.credential}
            </p>
          </div>

          {/* Hello World placeholder */}
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.6 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-12 shadow-2xl border border-white/20 max-w-2xl mx-auto"
          >
            <div className="text-6xl mb-6">🌍</div>
            <h3 className="text-3xl font-bold text-white mb-4">Hello World</h3>
            <p className="text-blue-200 text-lg mb-6">
              Government search functionality will be implemented here.
            </p>
            
            {/* Status indicators */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
              <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4">
                <div className="text-green-400 font-semibold">✓ Authenticated</div>
                <div className="text-green-200 text-sm">Government access verified</div>
              </div>
              <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4">
                <div className="text-blue-400 font-semibold">🔒 Secure</div>
                <div className="text-blue-200 text-sm">Enterprise-grade security</div>
              </div>
              <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4">
                <div className="text-yellow-400 font-semibold">🚧 Development</div>
                <div className="text-yellow-200 text-sm">Search features coming soon</div>
              </div>
            </div>
          </motion.div>

          {/* Additional info */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="mt-12 text-center"
          >
            <p className="text-blue-300 text-sm">
              This is a placeholder page for the government search interface.
            </p>
            <p className="text-blue-400 text-xs mt-2">
              Session started: {new Date(sessionData?.loginTime).toLocaleString()}
            </p>
          </motion.div>
        </motion.div>
      </main>

      {/* Footer */}
      <footer className="absolute bottom-0 w-full bg-black/20 backdrop-blur-lg border-t border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="text-center text-blue-200 text-sm">
            <p>FaceTrace Government Portal - Authorized Use Only</p>
            <p className="text-xs text-blue-300 mt-1">
              © 2024 FaceTrace Enterprise. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
