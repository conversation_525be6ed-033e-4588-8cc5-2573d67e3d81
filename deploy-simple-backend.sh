#!/bin/bash

# Simple FaceTrace Backend API Deployment
# Uses gcloud run deploy with source code directly

set -e

PROJECT_ID="facetracepro"
SERVICE_NAME="facetrace-backend-api"
REGION="us-central1"

echo "🚀 Simple FaceTrace Backend API deployment..."

# Step 1: Authenticate and set project
echo "📋 Step 1: Setting up Google Cloud..."
gcloud config set project $PROJECT_ID

echo "🔧 Enabling required APIs..."
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com

# Step 2: Deploy directly from source
echo "☁️ Step 2: Deploying from source code..."
gcloud run deploy $SERVICE_NAME \
  --source . \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --timeout 3600 \
  --concurrency 1000 \
  --max-instances 10 \
  --min-instances 0 \
  --project $PROJECT_ID \
  --set-env-vars NODE_ENV=production,NEXT_PUBLIC_DISABLE_AUTH=true,NEXT_PUBLIC_DISABLE_PAYMENT=true,DATABASE_URL=postgresql://dummy:dummy@localhost:5432/dummy

echo "✅ Deployment completed!"
echo "🌐 Your backend API is available at:"
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)' 2>/dev/null || echo "Check Cloud Run console")
echo $SERVICE_URL

echo ""
echo "📝 Next steps:"
echo "1. Configure real environment variables"
echo "2. Test your API endpoints"
echo "3. Update your frontend to use: $SERVICE_URL"
