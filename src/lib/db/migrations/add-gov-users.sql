-- Migration: Add government users table
-- Created: 2024-01-XX
-- Description: Creates the gov_users table for government entity authentication

CREATE TABLE IF NOT EXISTS gov_users (
    id SERIAL PRIMARY KEY,
    entity VARCHAR(255) NOT NULL,
    credential TEXT NOT NULL UNIQUE,
    access_code TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true NOT NULL,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS gov_users_entity_idx ON gov_users(entity);
CREATE UNIQUE INDEX IF NOT EXISTS gov_users_credential_key ON gov_users(credential);

-- Insert test data
INSERT INTO gov_users (entity, credential, access_code, is_active) 
VALUES ('test', '<EMAIL>', 'bryce<PERSON>ens123', true)
ON CONFLICT (credential) DO NOTHING;

-- Add comment to table
COMMENT ON TABLE gov_users IS 'Government entity users for FaceTrace Gov portal authentication';
