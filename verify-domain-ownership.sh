#!/bin/bash

# Domain Ownership Verification for Google Cloud
# This script helps verify domain ownership for Google Cloud services

set -e

# Configuration
PROJECT_ID="facetracepro"
DOMAIN_NAME="facetrace.pro"  # Change this to your actual domain
ZONE_NAME="facetrace-pro-zone"

echo "🔍 Domain Ownership Verification for $DOMAIN_NAME"
echo "📋 Project: $PROJECT_ID"
echo ""

# Step 1: Check current authentication
echo "📋 Step 1: Checking Google Cloud authentication..."
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "🔐 Please authenticate with Google Cloud..."
    gcloud auth login
else
    echo "✅ Already authenticated"
fi

gcloud config set project $PROJECT_ID

# Step 2: Enable required APIs
echo "🔧 Step 2: Enabling required APIs..."
gcloud services enable domains.googleapis.com
gcloud services enable dns.googleapis.com

# Step 3: Check domain verification status
echo "🔍 Step 3: Checking domain verification status..."
echo ""
echo "📝 To verify domain ownership, you have several options:"
echo ""
echo "🌐 Option 1: Google Search Console (Recommended)"
echo "   1. Go to: https://search.google.com/search-console"
echo "   2. Add property: $DOMAIN_NAME"
echo "   3. Choose verification method:"
echo "      - HTML file upload"
echo "      - HTML tag"
echo "      - DNS record"
echo "      - Google Analytics"
echo "      - Google Tag Manager"
echo ""

# Step 4: DNS verification method
echo "🔧 Option 2: DNS TXT Record Verification"
echo "   If you choose DNS verification, you'll need to add a TXT record"
echo "   to your DNS zone. Here's how to do it with Google Cloud DNS:"
echo ""

read -p "Do you want to add a DNS verification record now? (y/n): " add_verification

if [[ $add_verification == "y" || $add_verification == "Y" ]]; then
    echo ""
    echo "📋 To get your verification TXT record:"
    echo "1. Go to Google Search Console: https://search.google.com/search-console"
    echo "2. Add your domain: $DOMAIN_NAME"
    echo "3. Choose 'DNS record' verification method"
    echo "4. Copy the TXT record value"
    echo ""
    
    read -p "Enter the TXT record value from Google Search Console: " txt_value
    
    if [[ -n "$txt_value" ]]; then
        echo "📝 Adding TXT record for domain verification..."
        
        # Check if DNS zone exists
        if gcloud dns managed-zones describe $ZONE_NAME --project=$PROJECT_ID &>/dev/null; then
            gcloud dns record-sets create $DOMAIN_NAME. \
                --zone=$ZONE_NAME \
                --type=TXT \
                --ttl=300 \
                --rrdatas="\"$txt_value\"" \
                --project=$PROJECT_ID || echo "TXT record may already exist"
            
            echo "✅ TXT record added successfully!"
            echo "⏱️  Wait a few minutes for DNS propagation, then verify in Search Console"
        else
            echo "❌ DNS zone $ZONE_NAME not found. Please run migrate-domain-to-gcloud.sh first"
        fi
    fi
fi

# Step 5: HTML file verification method
echo ""
echo "🔧 Option 3: HTML File Verification"
echo "   If you choose HTML file verification:"
echo "   1. Download the verification file from Google Search Console"
echo "   2. Upload it to your website's root directory"
echo "   3. Make sure it's accessible at: http://$DOMAIN_NAME/[filename].html"
echo ""

# Step 6: Check current DNS records
echo "🔍 Step 4: Current DNS configuration for $DOMAIN_NAME"
if gcloud dns managed-zones describe $ZONE_NAME --project=$PROJECT_ID &>/dev/null; then
    echo "📋 Current DNS records:"
    gcloud dns record-sets list --zone=$ZONE_NAME --project=$PROJECT_ID --filter="type=TXT OR type=A OR type=CNAME"
else
    echo "⚠️ DNS zone not found in Google Cloud. Current external DNS:"
    echo "📋 Checking external DNS records..."
    
    # Check external DNS
    echo "🔍 A records:"
    dig +short A $DOMAIN_NAME || echo "No A records found"
    
    echo "🔍 TXT records:"
    dig +short TXT $DOMAIN_NAME || echo "No TXT records found"
    
    echo "🔍 Name servers:"
    dig +short NS $DOMAIN_NAME || echo "No NS records found"
fi

# Step 7: Verification checklist
echo ""
echo "✅ Domain Verification Checklist:"
echo ""
echo "□ 1. Domain added to Google Search Console"
echo "□ 2. Verification method chosen (DNS, HTML file, etc.)"
echo "□ 3. Verification record/file added"
echo "□ 4. Verification completed in Search Console"
echo "□ 5. Domain ownership confirmed"
echo ""
echo "📝 After verification:"
echo "□ 6. Run migrate-domain-to-gcloud.sh to set up Google Cloud DNS"
echo "□ 7. Update name servers at your registrar"
echo "□ 8. Set up Cloud Run domain mappings"
echo ""

# Step 8: Useful commands and links
echo "🔗 Useful links:"
echo "   Google Search Console: https://search.google.com/search-console"
echo "   Google Cloud DNS Console: https://console.cloud.google.com/net-services/dns?project=$PROJECT_ID"
echo "   Domain verification help: https://support.google.com/webmasters/answer/9008080"
echo ""
echo "🔧 Useful commands:"
echo "   Check DNS propagation: dig TXT $DOMAIN_NAME"
echo "   Check name servers: dig NS $DOMAIN_NAME"
echo "   List DNS records: gcloud dns record-sets list --zone=$ZONE_NAME"
echo ""
echo "📞 Need help?"
echo "   - Check Google Search Console for verification status"
echo "   - Verify DNS records are properly configured"
echo "   - Ensure domain is accessible and not redirecting"
echo ""
echo "✅ Domain verification setup completed!"
echo "📝 Next: Complete verification in Google Search Console, then run migrate-domain-to-gcloud.sh"
