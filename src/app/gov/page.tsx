'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/navigation';

// Government login page component
export default function GovLoginPage() {
  const router = useRouter();
  const [entity, setEntity] = useState('');
  const [credential, setCredential] = useState('');
  const [accessCode, setAccessCode] = useState('');
  const [isCredentialEnabled, setIsCredentialEnabled] = useState(false);
  const [isAccessCodeEnabled, setIsAccessCodeEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Check if entity is valid (for demo, we'll use "test" as valid)
  useEffect(() => {
    const validEntities = ['test', 'fbi', 'dhs', 'police', 'sheriff'];
    setIsCredentialEnabled(validEntities.includes(entity.toLowerCase()));
    if (!validEntities.includes(entity.toLowerCase())) {
      setCredential('');
      setAccessCode('');
      setIsAccessCodeEnabled(false);
    }
  }, [entity]);

  // Enable access code field when credential is entered
  useEffect(() => {
    setIsAccessCodeEnabled(isCredentialEnabled && credential.includes('@'));
    if (!credential.includes('@')) {
      setAccessCode('');
    }
  }, [credential, isCredentialEnabled]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/gov/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          entity,
          credential,
          accessCode,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Store session data
        localStorage.setItem('govSession', JSON.stringify({
          entity,
          credential,
          loginTime: Date.now(),
        }));
        
        // Redirect to government search page
        router.push('/gov/search');
      } else {
        setError(data.error || 'Authentication failed');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center p-4">
      {/* Animated background pattern */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-800/20 to-blue-900/20 opacity-20"></div>
        <motion.div
          className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20"
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            repeatType: "reverse",
          }}
        />
        <motion.div
          className="absolute top-3/4 right-1/4 w-96 h-96 bg-red-500 rounded-full mix-blend-multiply filter blur-xl opacity-20"
          animate={{
            x: [0, -100, 0],
            y: [0, 100, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            repeatType: "reverse",
          }}
        />
      </div>

      {/* Main login container */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="relative z-10 w-full max-w-md"
      >
        {/* Logo and branding section */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            className="relative mb-6"
          >
            {/* Enterprise text in circular arrangement */}
            <div className="relative w-32 h-32 mx-auto mb-4">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-xs font-bold text-blue-300 tracking-widest transform -rotate-12 absolute -top-2">
                  ENTERPRISE
                </div>
              </div>
              
              {/* FaceTrace logo placeholder - you can replace with actual SVG */}
              <div className="w-24 h-24 mx-auto bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-2xl">
                <div className="text-white font-bold text-lg">FT</div>
              </div>
            </div>

            {/* FaceTrace Gov branding */}
            <div className="space-y-2">
              <h1 className="text-4xl font-bold text-white">
                FaceTrace
                <span className="bg-gradient-to-r from-blue-400 to-red-500 bg-clip-text text-transparent ml-2">
                  Gov
                </span>
              </h1>
              <p className="text-blue-200 text-sm font-medium tracking-wide">
                GOVERNMENT ENTERPRISE ACCESS
              </p>
            </div>
          </motion.div>
        </div>

        {/* Login form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
          className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 shadow-2xl border border-white/20"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Entity field */}
            <div>
              <label htmlFor="entity" className="block text-sm font-medium text-blue-100 mb-2">
                Government Entity
              </label>
              <input
                type="text"
                id="entity"
                value={entity}
                onChange={(e) => setEntity(e.target.value)}
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all"
                placeholder="Enter entity name"
                required
              />
            </div>

            {/* Credential field */}
            <div>
              <label htmlFor="credential" className="block text-sm font-medium text-blue-100 mb-2">
                Authorized Credential
              </label>
              <input
                type="email"
                id="credential"
                value={credential}
                onChange={(e) => setCredential(e.target.value)}
                disabled={!isCredentialEnabled}
                className={`w-full px-4 py-3 border rounded-lg transition-all ${
                  isCredentialEnabled
                    ? 'bg-white/10 border-white/20 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent'
                    : 'bg-gray-500/20 border-gray-500/30 text-gray-400 cursor-not-allowed'
                }`}
                placeholder="Enter authorized email"
                required
              />
            </div>

            {/* Access code field */}
            <div>
              <label htmlFor="accessCode" className="block text-sm font-medium text-blue-100 mb-2">
                Access Code
              </label>
              <input
                type="password"
                id="accessCode"
                value={accessCode}
                onChange={(e) => setAccessCode(e.target.value)}
                disabled={!isAccessCodeEnabled}
                className={`w-full px-4 py-3 border rounded-lg transition-all ${
                  isAccessCodeEnabled
                    ? 'bg-white/10 border-white/20 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent'
                    : 'bg-gray-500/20 border-gray-500/30 text-gray-400 cursor-not-allowed'
                }`}
                placeholder="Enter access code"
                required
              />
            </div>

            {/* Error message */}
            <AnimatePresence>
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="bg-red-500/20 border border-red-500/30 rounded-lg p-3 text-red-200 text-sm"
                >
                  {error}
                </motion.div>
              )}
            </AnimatePresence>

            {/* Submit button */}
            <button
              type="submit"
              disabled={!isAccessCodeEnabled || isLoading}
              className={`w-full py-3 rounded-lg font-semibold transition-all ${
                isAccessCodeEnabled && !isLoading
                  ? 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02]'
                  : 'bg-gray-500/30 text-gray-400 cursor-not-allowed'
              }`}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                  Authenticating...
                </div>
              ) : (
                'Access Government Portal'
              )}
            </button>
          </form>

          {/* Security notice */}
          <div className="mt-6 text-center">
            <p className="text-xs text-blue-200/70">
              🔒 Secure government access portal
            </p>
            <p className="text-xs text-blue-200/50 mt-1">
              Authorized personnel only
            </p>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}
