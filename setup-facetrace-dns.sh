#!/bin/bash

# FaceTrace.pro Google Cloud DNS Setup Script
# This script configures facetrace.pro to use Google Cloud DNS with complete DNS management

set -e

# Configuration
PROJECT_ID="facetracepro"
DOMAIN_NAME="facetrace.pro"
ZONE_NAME="facetrace-pro-zone"
REGION="us-central1"

# Expected Google Cloud nameservers
EXPECTED_NS=(
    "ns-cloud-a1.googledomains.com."
    "ns-cloud-a2.googledomains.com."
    "ns-cloud-a3.googledomains.com."
    "ns-cloud-a4.googledomains.com."
)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "\n${BLUE}🔧 $1${NC}"
}

# Error handling
handle_error() {
    log_error "Script failed at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check gcloud authentication
check_auth() {
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "Not authenticated with Google Cloud"
        log_info "Please run: gcloud auth login"
        exit 1
    fi
    
    local current_account=$(gcloud auth list --filter=status:ACTIVE --format="value(account)" 2>/dev/null)
    log_success "Authenticated as: $current_account"
}

# Function to set project
set_project() {
    log_step "Setting Google Cloud project to $PROJECT_ID"
    gcloud config set project $PROJECT_ID
    log_success "Project set to: $PROJECT_ID"
}

# Function to enable required APIs
enable_apis() {
    log_step "Enabling required Google Cloud APIs"
    local apis=("dns.googleapis.com" "domains.googleapis.com" "run.googleapis.com")
    
    for api in "${apis[@]}"; do
        log_info "Enabling $api..."
        gcloud services enable $api --project=$PROJECT_ID
    done
    
    log_success "All required APIs enabled"
}

# Function to create DNS zone
create_dns_zone() {
    log_step "Creating DNS zone for $DOMAIN_NAME"
    
    if gcloud dns managed-zones describe $ZONE_NAME --project=$PROJECT_ID &>/dev/null; then
        log_warning "DNS zone '$ZONE_NAME' already exists"
        return 0
    fi
    
    gcloud dns managed-zones create $ZONE_NAME \
        --description="DNS zone for $DOMAIN_NAME managed by Google Cloud" \
        --dns-name=$DOMAIN_NAME \
        --project=$PROJECT_ID
    
    log_success "DNS zone '$ZONE_NAME' created successfully"
}

# Function to get nameservers
get_nameservers() {
    log_step "Getting Google Cloud nameservers"
    
    local nameservers=$(gcloud dns managed-zones describe $ZONE_NAME \
        --project=$PROJECT_ID \
        --format="value(nameServers[])" 2>/dev/null)
    
    if [[ -n "$nameservers" ]]; then
        log_info "Your Google Cloud nameservers:"
        echo "$nameservers" | tr ';' '\n' | while read ns; do
            if [[ -n "$ns" ]]; then
                echo "   $ns"
            fi
        done
    else
        log_error "Could not retrieve nameservers"
        return 1
    fi
}

# Function to setup DNS records
setup_dns_records() {
    log_step "Setting up DNS records"
    
    # A record for main domain (current Vercel IP)
    log_info "Creating A record for $DOMAIN_NAME"
    gcloud dns record-sets create $DOMAIN_NAME. \
        --zone=$ZONE_NAME \
        --type=A \
        --ttl=300 \
        --rrdatas=*********** \
        --project=$PROJECT_ID || log_warning "A record may already exist"
    
    # CNAME for www subdomain
    log_info "Creating CNAME record for www.$DOMAIN_NAME"
    gcloud dns record-sets create www.$DOMAIN_NAME. \
        --zone=$ZONE_NAME \
        --type=CNAME \
        --ttl=300 \
        --rrdatas=cname.vercel-dns.com. \
        --project=$PROJECT_ID || log_warning "CNAME record may already exist"
    
    # Placeholder CNAME records for future subdomains
    log_info "Creating placeholder CNAME records for subdomains"
    
    # API subdomain (will be updated when Cloud Run service is deployed)
    gcloud dns record-sets create api.$DOMAIN_NAME. \
        --zone=$ZONE_NAME \
        --type=CNAME \
        --ttl=300 \
        --rrdatas=ghs.googlehosted.com. \
        --project=$PROJECT_ID || log_warning "API CNAME record may already exist"
    
    # Government portal subdomain
    gcloud dns record-sets create gov.$DOMAIN_NAME. \
        --zone=$ZONE_NAME \
        --type=CNAME \
        --ttl=300 \
        --rrdatas=ghs.googlehosted.com. \
        --project=$PROJECT_ID || log_warning "Gov CNAME record may already exist"
    
    log_success "DNS records created successfully"
}

# Function to setup MX records (basic email)
setup_mx_records() {
    log_step "Setting up MX records for email"
    
    log_info "Creating basic MX records (Google Workspace compatible)"
    gcloud dns record-sets create $DOMAIN_NAME. \
        --zone=$ZONE_NAME \
        --type=MX \
        --ttl=3600 \
        --rrdatas="1 aspmx.l.google.com.,5 alt1.aspmx.l.google.com.,5 alt2.aspmx.l.google.com.,10 alt3.aspmx.l.google.com.,10 alt4.aspmx.l.google.com." \
        --project=$PROJECT_ID || log_warning "MX records may already exist"
    
    log_success "MX records configured"
}

# Function to setup TXT records
setup_txt_records() {
    log_step "Setting up TXT records"

    # SPF record for email
    log_info "Creating SPF record"
    gcloud dns record-sets create $DOMAIN_NAME. \
        --zone=$ZONE_NAME \
        --type=TXT \
        --ttl=300 \
        --rrdatas="\"v=spf1 include:_spf.google.com ~all\"" \
        --project=$PROJECT_ID || log_warning "SPF record may already exist"

    # Add domain verification placeholder (user can add specific verification records later)
    log_info "TXT records configured. Add domain verification records manually if needed."

    log_success "TXT records configured"
}

# Function to verify DNS configuration
verify_dns_config() {
    log_step "Verifying DNS configuration"
    
    log_info "Current DNS records in Google Cloud:"
    gcloud dns record-sets list --zone=$ZONE_NAME --project=$PROJECT_ID
    
    log_info "Checking external DNS resolution..."
    if command_exists dig; then
        log_info "Current nameservers (external view):"
        dig +short NS $DOMAIN_NAME || log_warning "Could not query external DNS"
        
        log_info "Current A record (external view):"
        dig +short A $DOMAIN_NAME || log_warning "Could not query A record"
    else
        log_warning "dig command not available for external DNS verification"
    fi
}

# Function to display registrar instructions
show_registrar_instructions() {
    log_step "Domain Registrar Update Instructions"
    
    echo ""
    echo "🌐 To complete the DNS migration, update your nameservers at your domain registrar:"
    echo ""
    echo "📋 Replace your current nameservers with these Google Cloud nameservers:"
    
    local nameservers=$(gcloud dns managed-zones describe $ZONE_NAME \
        --project=$PROJECT_ID \
        --format="value(nameServers[])" 2>/dev/null)
    
    if [[ -n "$nameservers" ]]; then
        echo "$nameservers" | tr ';' '\n' | while read ns; do
            if [[ -n "$ns" ]]; then
                echo "   $ns"
            fi
        done
    fi
    
    echo ""
    echo "📝 Steps for common registrars:"
    echo ""
    echo "🔹 GoDaddy:"
    echo "   1. Log into your GoDaddy account"
    echo "   2. Go to My Products → Domains → $DOMAIN_NAME"
    echo "   3. Click 'DNS' or 'Manage DNS'"
    echo "   4. Scroll down to 'Nameservers'"
    echo "   5. Click 'Change' and select 'Custom'"
    echo "   6. Replace with the Google Cloud nameservers above"
    echo "   7. Save changes"
    echo ""
    echo "🔹 Namecheap:"
    echo "   1. Log into Namecheap"
    echo "   2. Go to Domain List → Manage → $DOMAIN_NAME"
    echo "   3. Select 'Custom DNS' from Nameservers dropdown"
    echo "   4. Enter the Google Cloud nameservers"
    echo "   5. Save changes"
    echo ""
    echo "🔹 Cloudflare:"
    echo "   1. Remove domain from Cloudflare (if currently using)"
    echo "   2. Update nameservers at your actual registrar"
    echo ""
    echo "⏱️  DNS propagation can take 24-48 hours"
    echo "🔍 Monitor propagation: dig NS $DOMAIN_NAME"
}

# Function to show next steps
show_next_steps() {
    log_step "Next Steps"
    
    echo ""
    echo "✅ DNS zone setup completed!"
    echo ""
    echo "📝 What's been configured:"
    echo "   ✓ Google Cloud DNS zone created"
    echo "   ✓ A record: $DOMAIN_NAME → *********** (current Vercel)"
    echo "   ✓ CNAME: www.$DOMAIN_NAME → cname.vercel-dns.com"
    echo "   ✓ CNAME: api.$DOMAIN_NAME → ghs.googlehosted.com (ready for Cloud Run)"
    echo "   ✓ CNAME: gov.$DOMAIN_NAME → ghs.googlehosted.com (ready for Cloud Run)"
    echo "   ✓ MX records for email (Google Workspace compatible)"
    echo "   ✓ SPF record for email security"
    echo ""
    echo "🚀 After updating nameservers at registrar:"
    echo "   1. Wait for DNS propagation (24-48 hours)"
    echo "   2. Deploy Cloud Run services: ./deploy-gov.sh"
    echo "   3. Set up domain mappings: ./setup-gov-domain.sh"
    echo "   4. Test all subdomains and services"
    echo ""
    echo "🔍 Monitor DNS status:"
    echo "   ./domain-status-check.sh"
    echo ""
    echo "📊 View DNS records:"
    echo "   gcloud dns record-sets list --zone=$ZONE_NAME --project=$PROJECT_ID"
}

# Main execution
main() {
    echo "🌐 FaceTrace.pro Google Cloud DNS Setup"
    echo "========================================"
    echo "Domain: $DOMAIN_NAME"
    echo "Project: $PROJECT_ID"
    echo "Zone: $ZONE_NAME"
    echo ""
    
    # Check prerequisites
    if ! command_exists gcloud; then
        log_error "Google Cloud CLI not found. Please install from: https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    
    # Execute setup steps
    check_auth
    set_project
    enable_apis
    create_dns_zone
    get_nameservers
    setup_dns_records
    setup_mx_records
    setup_txt_records
    verify_dns_config
    show_registrar_instructions
    show_next_steps
    
    log_success "DNS setup completed successfully!"
}

# Run main function
main "$@"
