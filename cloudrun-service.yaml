apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: facetrace-backend-api
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # Resource allocation
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/cpu: "2"
        
        # Scaling configuration
        autoscaling.knative.dev/maxScale: "10"
        autoscaling.knative.dev/minScale: "0"
        
        # Timeout configuration (1 hour for long-running operations)
        run.googleapis.com/timeout: "3600s"
        
        # Concurrency
        run.googleapis.com/execution-environment: gen2
        
    spec:
      containerConcurrency: 1000
      timeoutSeconds: 3600
      containers:
      - image: gcr.io/facetracepro/facetrace-backend-api
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: NEXT_PUBLIC_DISABLE_AUTH
          value: "true"
        - name: NEXT_PUBLIC_DISABLE_PAYMENT
          value: "true"
        - name: PORT
          value: "3000"
        - name: LOG_LEVEL
          value: "info"
        
        # Resource limits
        resources:
          limits:
            memory: "2Gi"
            cpu: "2000m"
          requests:
            memory: "1Gi"
            cpu: "1000m"
            
        # Health checks
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          
  traffic:
  - percent: 100
    latestRevision: true
