# Domain Migration to Google Cloud CLI Guide

This comprehensive guide will help you migrate your domain management from your current DNS provider to Google Cloud DNS, enabling full integration with Google Cloud services.

## 🎯 Overview

Moving your domain to Google Cloud DNS provides:
- **Seamless integration** with Cloud Run services
- **Automatic SSL certificates** for custom domains
- **High availability** and global DNS resolution
- **Easy subdomain management** for government portal
- **Centralized management** through gcloud CLI

## 📋 Prerequisites

Before starting the migration:

1. **Domain ownership** - You must own the domain (facetrace.pro)
2. **Google Cloud CLI** - Installed and configured
3. **Domain registrar access** - To update name servers
4. **Google Search Console** - For domain verification

## 🚀 Migration Process

### Step 1: Check Current Domain Status

First, analyze your current domain configuration:

```bash
# Make script executable
chmod +x domain-status-check.sh

# Run domain status check
./domain-status-check.sh
```

This script will:
- ✅ Check Google Cloud authentication
- 🌐 Analyze current DNS configuration
- 🗺️ Check if Google Cloud DNS zone exists
- ☁️ Review Cloud Run services
- 📝 Provide migration recommendations

### Step 2: Verify Domain Ownership

Before migrating, verify domain ownership with Google:

```bash
# Run domain verification script
./verify-domain-ownership.sh
```

This script helps you:
- 🔍 Set up Google Search Console verification
- 📝 Add DNS TXT records for verification
- 🌐 Choose verification method (DNS, HTML file, etc.)

**Manual steps required:**
1. Go to [Google Search Console](https://search.google.com/search-console)
2. Add your domain: `facetrace.pro`
3. Complete verification using your preferred method

### Step 3: Migrate Domain to Google Cloud DNS

Once domain ownership is verified:

```bash
# Run domain migration script
./migrate-domain-to-gcloud.sh
```

This script will:
- 🗺️ Create Google Cloud DNS zone
- 📡 Set up name servers
- 🔧 Add common DNS records (A, CNAME, etc.)
- 🔗 Configure Cloud Run domain mappings
- 📋 Provide name servers for registrar update

### Step 4: Update Name Servers at Registrar

After running the migration script:

1. **Copy the Google Cloud name servers** provided by the script
2. **Log into your domain registrar** (GoDaddy, Namecheap, etc.)
3. **Update name servers** to the Google Cloud ones
4. **Save changes** and wait for propagation

Example name servers (yours will be different):
```
ns-cloud-a1.googledomains.com.
ns-cloud-a2.googledomains.com.
ns-cloud-a3.googledomains.com.
ns-cloud-a4.googledomains.com.
```

### Step 5: Wait for DNS Propagation

DNS changes can take up to 48 hours to propagate globally. Monitor progress:

```bash
# Check name servers
dig NS facetrace.pro

# Check if using Google DNS
nslookup facetrace.pro

# Monitor propagation
watch -n 30 'dig NS facetrace.pro'
```

### Step 6: Set Up Subdomains

Once migration is complete, set up subdomains for your services:

```bash
# Set up government portal subdomain
./setup-gov-domain.sh

# Set up API subdomain (if needed)
./setup-custom-domain.sh
```

## 📁 Scripts Overview

### `domain-status-check.sh`
- **Purpose**: Analyze current domain configuration
- **When to use**: Before starting migration
- **Output**: Current DNS status and migration recommendations

### `verify-domain-ownership.sh`
- **Purpose**: Help verify domain ownership with Google
- **When to use**: Before domain migration
- **Output**: Verification instructions and DNS record setup

### `migrate-domain-to-gcloud.sh`
- **Purpose**: Create Google Cloud DNS zone and migrate domain
- **When to use**: After domain verification
- **Output**: DNS zone, records, and name servers for registrar

### `setup-gov-domain.sh`
- **Purpose**: Set up gov.facetrace.pro subdomain
- **When to use**: After domain migration
- **Output**: Government portal domain mapping

## 🔧 Configuration

### Domain Settings

Update these variables in the scripts if needed:

```bash
PROJECT_ID="facetracepro"
DOMAIN_NAME="facetrace.pro"  # Your actual domain
ZONE_NAME="facetrace-pro-zone"
REGION="us-central1"
```

### Services Configuration

The scripts will set up domains for these services:
- **Main domain**: `facetrace.pro`
- **API subdomain**: `api.facetrace.pro`
- **Government portal**: `gov.facetrace.pro`
- **WWW subdomain**: `www.facetrace.pro`

## 🔍 Verification and Testing

### Check Migration Status

```bash
# Run status check after migration
./domain-status-check.sh

# Check DNS records in Google Cloud
gcloud dns record-sets list --zone=facetrace-pro-zone

# Test domain resolution
dig A facetrace.pro
dig CNAME gov.facetrace.pro
```

### Test Services

After migration and propagation:

```bash
# Test main domain (if configured)
curl https://facetrace.pro

# Test API subdomain
curl https://api.facetrace.pro/api/health

# Test government portal
curl https://gov.facetrace.pro/gov
```

## 🆘 Troubleshooting

### Common Issues

1. **DNS not propagating**
   - Wait longer (up to 48 hours)
   - Check with multiple DNS checkers
   - Verify name servers at registrar

2. **Domain verification fails**
   - Ensure verification record is correct
   - Wait for DNS propagation
   - Try alternative verification method

3. **SSL certificate issues**
   - Wait for automatic provisioning (can take 15 minutes)
   - Check domain mapping status
   - Verify DNS is pointing correctly

### Useful Commands

```bash
# Check DNS propagation
dig NS facetrace.pro @*******
dig A facetrace.pro @*******

# Check Google Cloud DNS
gcloud dns managed-zones list
gcloud dns record-sets list --zone=facetrace-pro-zone

# Check domain mappings
gcloud run domain-mappings list --region=us-central1

# Check SSL certificate status
gcloud run domain-mappings describe gov.facetrace.pro --region=us-central1
```

## 📞 Support Resources

- **Google Cloud DNS Documentation**: https://cloud.google.com/dns/docs
- **Domain verification help**: https://support.google.com/webmasters/answer/9008080
- **Cloud Run custom domains**: https://cloud.google.com/run/docs/mapping-custom-domains

## ✅ Migration Checklist

- [ ] Domain ownership verified in Google Search Console
- [ ] Google Cloud DNS zone created
- [ ] DNS records migrated to Google Cloud
- [ ] Name servers updated at registrar
- [ ] DNS propagation completed (24-48 hours)
- [ ] Subdomains configured for services
- [ ] SSL certificates provisioned
- [ ] All services accessible via custom domains
- [ ] Government portal accessible at gov.facetrace.pro

## 🎉 Success!

Once migration is complete, you'll have:
- **Full domain control** through Google Cloud CLI
- **Automatic SSL certificates** for all subdomains
- **Seamless integration** with Cloud Run services
- **High availability** DNS resolution
- **Easy subdomain management** for future services

Your FaceTrace services will be accessible at:
- **Main site**: `https://facetrace.pro`
- **API**: `https://api.facetrace.pro`
- **Government portal**: `https://gov.facetrace.pro`
