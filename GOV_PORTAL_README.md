# FaceTrace Government Portal

A private enterprise version of FaceTrace designed specifically for government entities with enhanced security and specialized authentication.

## 🏛️ Overview

The FaceTrace Government Portal provides authorized government entities with secure access to facial recognition and identity intelligence capabilities. This enterprise-grade solution features:

- **Government-specific authentication** with entity validation
- **Progressive form enablement** for enhanced security
- **Enterprise branding** with government styling
- **Secure session management** for authorized personnel
- **Dedicated subdomain** at `gov.facetrace.com`

## 🚀 Quick Start

### 1. Deploy the Government Portal

```bash
# Make deployment script executable
chmod +x deploy-gov.sh

# Deploy to Google Cloud Run
./deploy-gov.sh
```

### 2. Set up Custom Domain (Optional)

```bash
# Configure gov.facetrace.com subdomain
chmod +x setup-gov-domain.sh
./setup-gov-domain.sh
```

### 3. Test the Portal

Visit the deployed URL and test with the default credentials:
- **Entity**: `test`
- **Credential**: `<EMAIL>`
- **Access Code**: `brycebayens123`

## 🔐 Authentication System

### Progressive Form Fields

The government portal uses a three-step authentication process:

1. **Entity Field**: Government entity name validation
2. **Credential Field**: Email input (enabled when valid entity is entered)
3. **Access Code Field**: Password authentication (enabled when valid email is entered)

### Database Schema

The `gov_users` table stores government authentication data:

```sql
CREATE TABLE gov_users (
    id SERIAL PRIMARY KEY,
    entity VARCHAR(255) NOT NULL,
    credential TEXT NOT NULL UNIQUE,
    access_code TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true NOT NULL,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);
```

## 🎨 Design Features

### Government Branding

- **Logo**: "FaceTraceGov" with blue and red gradient "GOV" text
- **Layout**: "ENTERPRISE" text in circular arrangement above logo
- **Color Scheme**: USA government entity design patterns
- **Typography**: Professional government styling

### UI Components

- **Animated background** with government-themed patterns
- **Progressive field enablement** for enhanced UX
- **Real-time validation** and error handling
- **Responsive design** for various screen sizes

## 📁 File Structure

```
src/
├── app/
│   ├── gov/
│   │   ├── page.tsx              # Government login page
│   │   └── search/
│   │       └── page.tsx          # Government search interface
│   └── api/
│       └── gov/
│           └── auth/
│               └── route.ts      # Government authentication API
├── lib/
│   └── db/
│       ├── schema.ts             # Database schema with gov_users table
│       ├── index.ts              # Database functions for gov users
│       └── migrations/
│           └── add-gov-users.sql # Government users migration
└── middleware.ts                 # Updated to handle gov routes
```

## 🛠️ Development

### Local Development

```bash
# Install dependencies
npm install

# Run database migration
npm run db:migrate:gov

# Start development server
npm run dev

# Access government portal
open http://localhost:3000/gov
```

### Database Management

```bash
# Run government users migration
npm run db:migrate:gov

# Check database status
npm run db:ping:local

# View database stats
npm run db:stats:local
```

## 🌐 Deployment

### Google Cloud Run

The government portal is deployed as a separate service on Google Cloud Run with:

- **Service Name**: `facetrace-gov`
- **Region**: `us-central1`
- **Memory**: 2GB
- **CPU**: 2 vCPU
- **Timeout**: 1 hour
- **Auto-scaling**: 0-10 instances

### Environment Variables

```bash
NODE_ENV=production
NEXT_PUBLIC_DISABLE_AUTH=true
NEXT_PUBLIC_DISABLE_PAYMENT=true
GOV_PORTAL=true
DATABASE_URL=your_database_connection_string
```

### Custom Domain Setup

1. Verify domain ownership in Google Search Console
2. Run the domain setup script: `./setup-gov-domain.sh`
3. Configure DNS records as provided
4. Wait for DNS propagation (up to 24 hours)

## 🔒 Security Features

- **Entity validation** before credential field activation
- **Progressive authentication** with field-level security
- **Session management** with secure local storage
- **Government-grade encryption** for data transmission
- **Audit logging** for all authentication attempts

## 📊 API Endpoints

### Authentication

- `POST /api/gov/auth` - Government user authentication
- `GET /api/gov/auth` - Health check endpoint

### Portal Routes

- `/gov` - Government login page
- `/gov/search` - Government search interface (authenticated)

## 🧪 Testing

### Test Credentials

Default test user for development:
- **Entity**: `test`
- **Credential**: `<EMAIL>`
- **Access Code**: `brycebayens123`

### Manual Testing

1. Navigate to the government portal
2. Enter entity name "test"
3. Verify credential field becomes enabled
4. Enter test email address
5. Verify access code field becomes enabled
6. Enter access code and submit
7. Verify redirect to search page

## 📝 Next Steps

1. **Implement search functionality** in the government search interface
2. **Add additional government entities** to the validation system
3. **Integrate with existing FaceTrace search APIs** for government use
4. **Implement audit logging** for compliance requirements
5. **Add role-based access control** for different government levels

## 🆘 Troubleshooting

### Common Issues

1. **Database connection errors**: Verify DATABASE_URL is set correctly
2. **Authentication failures**: Check gov_users table exists and has test data
3. **Domain mapping issues**: Verify domain ownership and DNS configuration
4. **Deployment failures**: Check Google Cloud authentication and project settings

### Support

For technical support or questions about the government portal, please contact the development team or refer to the main FaceTrace documentation.
