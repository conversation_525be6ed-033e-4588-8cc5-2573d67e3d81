// src/lib/db/schema.ts
import { pgTable, serial, text, varchar, boolean, integer, timestamp, jsonb, decimal, uniqueIndex, index } from 'drizzle-orm/pg-core';
import { relations, sql } from 'drizzle-orm';

// Form helper for validation and type safety
export const formHelp = {
  users: {
    email: {
      required: true,
      label: 'Email',
      placeholder: '<EMAIL>'
    },
    name: {
      label: 'Name',
      placeholder: 'Full Name'
    }
  },
  searchReports: {
    userType: {
      label: 'User Type',
      options: ['guest', 'registered']
    },
    status: {
      label: 'Status',
      options: ['processing', 'completed', 'failed']
    }
  }
};

// Users table - matches the SQL schema
export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  clerkId: varchar('clerk_id', { length: 255 }).notNull().unique(),
  email: text('email').unique(),
  name: text('name'),
  verified: boolean('verified').default(false),
  tokens: integer('tokens').default(0).notNull(), // Unified token system for all features
  stripeCustomerId: text('stripe_customer_id'), // Stripe customer ID for registered users
  initialFreeUnlockUsed: boolean('initial_free_unlock_used').default(false).notNull(), // New field for tracking free unlock
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull()
}, (table) => {
  return {
    clerkIdIdx: uniqueIndex('users_clerk_id_key').on(table.clerkId),
    emailIdx: uniqueIndex('users_email_key').on(table.email)
  };
});

// Guest Users table - for non-authenticated users who make purchases
export const guestUsers = pgTable('guest_users', {
  id: text('id').primaryKey(), // nanoid
  email: text('email').notNull().unique(),
  stripeCustomerId: text('stripe_customer_id'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull()
}, (table) => {
  return {
    emailIdx: uniqueIndex('guest_users_email_key').on(table.email)
  };
});

// Government Users table - for government entity authentication
export const govUsers = pgTable('gov_users', {
  id: serial('id').primaryKey(),
  entity: varchar('entity', { length: 255 }).notNull(),
  credential: text('credential').notNull(), // Email
  accessCode: text('access_code').notNull(), // Hashed password
  isActive: boolean('is_active').default(true).notNull(),
  lastLogin: timestamp('last_login', { withTimezone: true }),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull()
}, (table) => {
  return {
    entityIdx: index('gov_users_entity_idx').on(table.entity),
    credentialIdx: uniqueIndex('gov_users_credential_key').on(table.credential)
  };
});

// Search Reports table - matches the SQL schema
export const searchReports = pgTable('search_reports', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').references(() => users.id, { onDelete: 'set null' }),
  email: text('email'),
  userType: varchar('user_type', { length: 50 }).default('guest').notNull(),
  facecheckIdSearch: text('facecheck_id_search').notNull(),
  status: varchar('status', { length: 50 }).default('processing').notNull(),
  progress: integer('progress').default(0).notNull(),
  resultCount: integer('result_count').default(0).notNull(),
  price: decimal('price', { precision: 10, scale: 2 }),
  isPrivate: boolean('is_private').default(false).notNull(),
  failureReason: text('failure_reason'),
  searchImageUrls: jsonb('search_image_urls'),
  expiresAt: timestamp('expires_at', { withTimezone: true }),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  report_id: text('report_id').unique(),
  stripe_pm_id: text('stripe_pm_id'),
}, (table) => {
  return {
    reportIdIdx: uniqueIndex('search_reports_report_id_key').on(table.report_id)
  };
});

// Uploads table - matches the SQL schema
export const uploads = pgTable('uploads', {
  id: serial('id').primaryKey(),
  id_search: text('id_search'),
  
  // First upload fields
  input_0: jsonb('input_0'),
  input_0_base64: text('input_0_base64'),
  input_0_id_pic: text('input_0_id_pic'),
  
  // Second upload fields
  input_1: jsonb('input_1'),
  input_1_base64: text('input_1_base64'),
  input_1_id_pic: text('input_1_id_pic'),
  
  // Third upload fields
  input_2: jsonb('input_2'),
  input_2_base64: text('input_2_base64'),
  input_2_id_pic: text('input_2_id_pic'),
  
  // Additional metadata
  new_seen_count: integer('new_seen_count').notNull().default(0),
  search_report_id: integer('search_report_id').references(() => searchReports.id, { onDelete: 'set null' }),
  duplicates: integer('duplicates').notNull().default(0),
  
  // Timestamps
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
});

// Guest Transactions table - matches the SQL schema
export const guestTransactions = pgTable('guest_transactions', {
  id: serial('id').primaryKey(),
  reportId: integer('report_id').notNull().references(() => searchReports.id, { onDelete: 'cascade' }),
  email: text('email').notNull(),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  currency: varchar('currency', { length: 3 }).default('usd').notNull(),
  status: varchar('status', { length: 50 }).default('pending').notNull(),
  stripePaymentId: text('stripe_payment_id').unique(),
  stripeSessionId: text('stripe_session_id'),
  failureReason: text('failure_reason'),
  attempts: integer('attempts').default(0).notNull(),
  metadata: jsonb('metadata'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => {
  return {
    stripePaymentIdIdx: uniqueIndex('guest_transactions_stripe_payment_id_key').on(table.stripePaymentId)
  };
});

// Report Transactions table - matches the SQL schema
export const reportTransactions = pgTable('report_transactions', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  reportId: integer('report_id').notNull().references(() => searchReports.id, { onDelete: 'cascade' }),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  currency: varchar('currency', { length: 3 }).default('usd').notNull(),
  status: varchar('status', { length: 50 }).default('pending').notNull(),
  stripePaymentId: text('stripe_payment_id').unique(),
  stripeSessionId: text('stripe_session_id'),
  failureReason: text('failure_reason'),
  attempts: integer('attempts').default(0).notNull(),
  metadata: jsonb('metadata'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => {
  return {
    stripePaymentIdIdx: uniqueIndex('report_transactions_stripe_payment_id_key').on(table.stripePaymentId)
  };
});

// Clerk Transactions table - tracks token usage and Stripe payments for registered users
export const clerkTransactions = pgTable('clerk_transactions', {
  id: text('id').primaryKey(), // nanoid
  userId: integer('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  clerkId: text('clerk_id').notNull(), // Store clerk ID for easier querying
  reportId: text('report_id'), // URL-safe report identifier (report_id column in searchReports)
  amount: decimal('amount', { precision: 10, scale: 2 }).default('0').notNull(),
  currency: varchar('currency', { length: 3 }).default('usd').notNull(),
  status: varchar('status', { length: 50 }).default('pending').notNull(),
  stripePaymentId: text('stripe_payment_id'),
  stripeCustomerId: text('stripe_customer_id'),
  tokensUsed: integer('tokens_used').default(0).notNull(), // Renamed from credits_used to tokens_used
  failureReason: text('failure_reason'),
  metadata: jsonb('metadata'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
});

// Refunds table - for tracking refunds
export const refunds = pgTable('refunds', {
  id: text('id').primaryKey(), // nanoid
  stripeRefundId: text('stripe_refund_id').unique(),
  stripePaymentId: text('stripe_payment_id').notNull(),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  currency: varchar('currency', { length: 3 }).default('usd').notNull(),
  status: varchar('status', { length: 50 }).default('pending').notNull(),
  reason: text('reason'),
  metadata: jsonb('metadata'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
});

// Opt-Out Requests table - for tracking face opt-out requests
export const optOutRequests = pgTable('opt_out_requests', {
  id: text('id').primaryKey(), // UUID
  fullName: text('full_name').notNull(),
  email: text('email').notNull(),
  status: varchar('status', { length: 50 }).default('pending').notNull(), // pending, processing, completed, rejected
  facePhotoPath: text('face_photo_path').notNull(),
  idPhotoPath: text('id_photo_path').notNull(),
  featureVector: jsonb('feature_vector'), // For storing facial recognition features
  requestDate: timestamp('request_date', { withTimezone: true }).defaultNow().notNull(),
  processedDate: timestamp('processed_date', { withTimezone: true }),
  processorUserId: integer('processor_user_id').references(() => users.id, { onDelete: 'set null' }),
  processingNotes: text('processing_notes'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => {
  return {
    emailIdx: index('opt_out_requests_email_idx').on(table.email),
    statusIdx: index('opt_out_requests_status_idx').on(table.status)
  };
});

// Search Results table - stores detailed search results
export const searchResults = pgTable('search_results', {
  id: serial('id').primaryKey(),
  searchReportId: integer('search_report_id').references(() => searchReports.id, { onDelete: 'cascade' }),
  uploadId: integer('upload_id').references(() => uploads.id, { onDelete: 'set null' }),
  sourceUrl: text('source_url'),
  thumbnail: text('thumbnail'),
  title: text('title'),
  domain: text('domain'),
  confidence: decimal('confidence', { precision: 10, scale: 2 }),
  rawData: jsonb('raw_data'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => {
  return {
    searchReportIdIdx: index('search_results_search_report_id_idx').on(table.searchReportId),
    uploadIdIdx: index('search_results_upload_id_idx').on(table.uploadId)
  };
});

// --- Relations - maintained from the original schema and extended ---
export const usersRelations = relations(users, ({ many }) => ({
  searchReports: many(searchReports),
  reportTransactions: many(reportTransactions),
  clerkTransactions: many(clerkTransactions),
}));

export const guestUsersRelations = relations(guestUsers, ({ many }) => ({
  // No direct relations needed yet
}));

export const searchReportsRelations = relations(searchReports, ({ one, many }) => ({
  user: one(users, {
    fields: [searchReports.userId],
    references: [users.id],
  }),
  uploads: many(uploads),
  guestTransactions: many(guestTransactions),
  reportTransactions: many(reportTransactions),
  searchResults: many(searchResults),
}));

export const uploadsRelations = relations(uploads, ({ one }) => ({
  searchReport: one(searchReports, {
    fields: [uploads.search_report_id],
    references: [searchReports.id],
  }),
}));

export const guestTransactionsRelations = relations(guestTransactions, ({ one }) => ({
  searchReport: one(searchReports, {
    fields: [guestTransactions.reportId],
    references: [searchReports.id],
  }),
}));

export const reportTransactionsRelations = relations(reportTransactions, ({ one }) => ({
  user: one(users, {
    fields: [reportTransactions.userId],
    references: [users.id],
  }),
  searchReport: one(searchReports, {
    fields: [reportTransactions.reportId],
    references: [searchReports.id],
  }),
}));

export const clerkTransactionsRelations = relations(clerkTransactions, ({ one }) => ({
  user: one(users, {
    fields: [clerkTransactions.userId],
    references: [users.id],
  }),
}));

export const refundsRelations = relations(refunds, ({ }) => ({
  // No direct relations yet, but we could link to transactions if needed
}));

export const optOutRequestsRelations = relations(optOutRequests, ({ one }) => ({
  processor: one(users, {
    fields: [optOutRequests.processorUserId],
    references: [users.id],
  }),
}));

export const searchResultsRelations = relations(searchResults, ({ one }) => ({
  searchReport: one(searchReports, {
    fields: [searchResults.searchReportId],
    references: [searchReports.id],
  }),
  upload: one(uploads, {
    fields: [searchResults.uploadId],
    references: [uploads.id],
  }),
}));
