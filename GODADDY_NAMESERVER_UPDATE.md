# GoDaddy Nameserver Update Guide

## 🎯 Quick Summary

Your Google Cloud DNS zone is ready! Now you need to update your nameservers at GoDaddy to complete the migration.

### 📋 Your Google Cloud Nameservers:
```
ns-cloud-a1.googledomains.com
ns-cloud-a2.googledomains.com
ns-cloud-a3.googledomains.com
ns-cloud-a4.googledomains.com
```

## 🔧 Step-by-Step GoDaddy Update

### Step 1: Log into GoDaddy
1. Go to [GoDaddy.com](https://www.godaddy.com)
2. Click "Sign In" and log into your account
3. Navigate to your account dashboard

### Step 2: Access Domain Management
1. Click on **"My Products"** in the top menu
2. Find **"Domains"** section
3. Locate **"facetrace.pro"** in your domain list
4. Click on the domain name or the **"DNS"** button next to it

### Step 3: Change Nameservers
1. On the DNS management page, scroll down to find **"Nameservers"** section
2. Click **"Change"** button next to the nameservers
3. Select **"Custom"** (not "Default" or "Parked")
4. You'll see input fields for nameservers

### Step 4: Enter Google Cloud Nameservers
Replace the existing nameservers with these **exact** values:

**Nameserver 1:** `ns-cloud-a1.googledomains.com`  
**Nameserver 2:** `ns-cloud-a2.googledomains.com`  
**Nameserver 3:** `ns-cloud-a3.googledomains.com`  
**Nameserver 4:** `ns-cloud-a4.googledomains.com`  

⚠️ **Important:** Do NOT include the trailing dot (.) when entering in GoDaddy

### Step 5: Save Changes
1. Click **"Save"** or **"Update Nameservers"**
2. Confirm the changes when prompted
3. You should see a confirmation message

## ⏱️ What Happens Next

### Immediate (0-15 minutes)
- Changes are saved in GoDaddy's system
- You may see a notice about propagation time

### 1-4 Hours
- Some DNS resolvers start picking up the new nameservers
- You can test with: `dig NS facetrace.pro`

### 24-48 Hours
- Full global DNS propagation
- All DNS queries will use Google Cloud DNS

## 🔍 Monitor Progress

### Check Current Status
```bash
# Check nameservers
dig NS facetrace.pro

# Monitor propagation
./monitor-dns-propagation.sh

# Continuous monitoring
./monitor-dns-propagation.sh --continuous
```

### Expected Results After Propagation
```bash
$ dig NS facetrace.pro
facetrace.pro.    21600   IN  NS  ns-cloud-a1.googledomains.com.
facetrace.pro.    21600   IN  NS  ns-cloud-a2.googledomains.com.
facetrace.pro.    21600   IN  NS  ns-cloud-a3.googledomains.com.
facetrace.pro.    21600   IN  NS  ns-cloud-a4.googledomains.com.
```

## 🚨 Troubleshooting

### If Changes Don't Appear
1. **Wait longer** - DNS propagation can take up to 48 hours
2. **Clear DNS cache** on your computer:
   - macOS: `sudo dscacheutil -flushcache`
   - Windows: `ipconfig /flushdns`
   - Linux: `sudo systemctl restart systemd-resolved`

### If You See Errors in GoDaddy
1. **Check nameserver format** - No trailing dots, exact spelling
2. **Try again** - Sometimes GoDaddy's interface has temporary issues
3. **Contact GoDaddy support** if the interface won't accept the nameservers

### If Propagation Seems Stuck
1. **Test multiple DNS servers**:
   ```bash
   dig @******* NS facetrace.pro      # Google DNS
   dig @******* NS facetrace.pro      # Cloudflare DNS
   dig @************** NS facetrace.pro # OpenDNS
   ```
2. **Check with online tools**:
   - [whatsmydns.net](https://www.whatsmydns.net)
   - [dnschecker.org](https://dnschecker.org)

## ✅ Verification Checklist

After updating nameservers at GoDaddy:

- [ ] Nameservers updated in GoDaddy account
- [ ] Confirmation received from GoDaddy
- [ ] DNS propagation monitoring started
- [ ] Nameservers showing Google Cloud (may take time)
- [ ] A record resolving correctly
- [ ] CNAME records working for subdomains

## 🚀 Next Steps After Propagation

Once DNS propagation is complete (nameservers show Google Cloud):

### 1. Deploy Services
```bash
# Deploy government portal
./deploy-gov.sh

# Deploy main backend API
./deploy-backend.sh
```

### 2. Set Up Domain Mappings
```bash
# Set up gov.facetrace.pro
./setup-gov-domain.sh

# Set up api.facetrace.pro (if needed)
./setup-custom-domain.sh
```

### 3. Test Everything
```bash
# Test main domain
curl https://facetrace.pro

# Test government portal
curl https://gov.facetrace.pro/gov

# Test API
curl https://api.facetrace.pro/api/health
```

## 📞 Support

### GoDaddy Support
- **Phone**: **************
- **Chat**: Available in your GoDaddy account
- **Help**: [GoDaddy DNS Help](https://www.godaddy.com/help/change-nameservers-for-my-domains-664)

### Google Cloud Support
- **Documentation**: [Cloud DNS Documentation](https://cloud.google.com/dns/docs)
- **Console**: [Google Cloud DNS Console](https://console.cloud.google.com/net-services/dns)

## 🎉 Success!

Once propagation is complete, you'll have:
- ✅ Full DNS control through Google Cloud
- ✅ Automatic SSL certificates for subdomains
- ✅ Seamless integration with Cloud Run
- ✅ Professional email setup (MX records)
- ✅ Ready for government portal deployment

Your domain will be fully managed by Google Cloud DNS, enabling easy subdomain management and integration with all Google Cloud services!
