#!/bin/bash

# FaceTrace Backend API Custom Domain Setup
# Configures a custom domain for the backend API service

set -e

PROJECT_ID="facetracepro"
SERVICE_NAME="facetrace-backend-api"
REGION="us-central1"

# Configuration - Update these values
DOMAIN_NAME="api.facetrace.pro"  # Replace with your desired domain
DOMAIN_ZONE="facetrace-pro"      # Replace with your Cloud DNS zone name

echo "🌐 Setting up custom domain for FaceTrace Backend API..."
echo "Domain: $DOMAIN_NAME"
echo "Service: $SERVICE_NAME"
echo ""

# Enable required APIs
echo "🔧 Enabling required APIs..."
gcloud services enable domains.googleapis.com
gcloud services enable dns.googleapis.com

# Create domain mapping
echo "🔗 Creating domain mapping..."
gcloud run domain-mappings create \
  --service $SERVICE_NAME \
  --domain $DOMAIN_NAME \
  --region $REGION \
  --project $PROJECT_ID

# Get the required DNS records
echo "📋 Getting DNS configuration..."
MAPPING_INFO=$(gcloud run domain-mappings describe $DOMAIN_NAME --region $REGION --format="value(status.resourceRecords[0].rrdata,status.resourceRecords[0].type)")

echo "✅ Domain mapping created!"
echo ""
echo "📝 DNS Configuration Required:"
echo "Add the following DNS records to your domain:"
echo ""
echo "Type: CNAME"
echo "Name: api (or the subdomain you're using)"
echo "Value: ghs.googlehosted.com"
echo ""
echo "🔍 To verify the mapping status:"
echo "gcloud run domain-mappings describe $DOMAIN_NAME --region $REGION"
echo ""
echo "⏱️  Note: DNS propagation can take up to 24 hours"
echo "🔒 SSL certificate will be automatically provisioned once DNS is configured"
echo ""
echo "🧪 Test your custom domain once DNS propagates:"
echo "curl https://$DOMAIN_NAME/api/health"
