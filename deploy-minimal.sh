#!/bin/bash

# Minimal FaceTrace Deployment Script
# Focus on core deployment without extra features

set -e

PROJECT_ID="facetracepro"
REGION="us-central1"
SERVICE_NAME="facetrace"

echo "🚀 Minimal FaceTrace Deployment"
echo "Project: $PROJECT_ID"
echo "Service: $SERVICE_NAME"
echo "Region: $REGION"
echo ""

# Set project
echo "📋 Setting project..."
gcloud config set project $PROJECT_ID

# Enable APIs
echo "🔧 Enabling APIs..."
gcloud services enable run.googleapis.com cloudbuild.googleapis.com

# Create a minimal package.json for deployment
echo "📦 Creating minimal build configuration..."
cat > package-deploy.json << 'EOF'
{
  "name": "facetrace-app",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "build": "next build",
    "start": "next start"
  },
  "dependencies": {
    "next": "^15.3.0",
    "react": "^18.3.1",
    "react-dom": "^18.3.1"
  }
}
EOF

# Backup original package.json
cp package.json package.json.backup

# Use minimal package.json
cp package-deploy.json package.json

echo "🚀 Deploying with minimal configuration..."
gcloud run deploy $SERVICE_NAME \
    --source . \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --memory 1Gi \
    --cpu 1 \
    --timeout 300 \
    --set-env-vars "NODE_ENV=production" \
    --project $PROJECT_ID

# Restore original package.json
mv package.json.backup package.json
rm package-deploy.json

echo "✅ Deployment completed!"

# Get service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --project $PROJECT_ID --format 'value(status.url)' 2>/dev/null || echo "")
if [[ -n "$SERVICE_URL" ]]; then
    echo "🌐 Service URL: $SERVICE_URL"
else
    echo "⚠️ Could not retrieve service URL"
fi
