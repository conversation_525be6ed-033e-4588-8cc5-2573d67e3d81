#!/bin/bash

# FaceTrace Government Portal Custom Domain Setup
# This script sets up custom domain mapping for gov.facetrace.com

set -e

# Configuration
PROJECT_ID="facetracepro"
SERVICE_NAME="facetrace-gov"
REGION="us-central1"
DOMAIN="gov.facetrace.pro"

echo "🌐 Setting up custom domain for FaceTrace Government Portal..."

# Step 1: Verify domain ownership (if not already done)
echo "📋 Step 1: Domain verification..."
echo "🔍 Please ensure you have verified ownership of facetrace.com in Google Search Console"
echo "🔗 Visit: https://search.google.com/search-console"
echo ""
read -p "Have you verified domain ownership? (y/n): " verified

if [[ $verified != "y" && $verified != "Y" ]]; then
    echo "❌ Please verify domain ownership first and run this script again"
    exit 1
fi

# Step 2: Enable Domain Mapping API
echo "🔧 Step 2: Enabling Domain Mapping API..."
gcloud services enable domains.googleapis.com

# Step 3: Create domain mapping
echo "🗺️ Step 3: Creating domain mapping..."
gcloud run domain-mappings create \
  --service $SERVICE_NAME \
  --domain $DOMAIN \
  --region $REGION \
  --project $PROJECT_ID || {
    echo "⚠️ Domain mapping creation failed or already exists"
    echo "📝 Checking existing mappings..."
    gcloud run domain-mappings list --region $REGION --project $PROJECT_ID
}

# Step 4: Get DNS configuration
echo "📡 Step 4: Getting DNS configuration..."
echo "🔍 Please configure the following DNS records:"
echo ""

# Get the domain mapping details
MAPPING_INFO=$(gcloud run domain-mappings describe $DOMAIN --region $REGION --project $PROJECT_ID --format="value(status.resourceRecords[].name,status.resourceRecords[].rrdata)" 2>/dev/null || echo "")

if [ -n "$MAPPING_INFO" ]; then
    echo "📋 DNS Records to configure:"
    echo "$MAPPING_INFO" | while read line; do
        if [ -n "$line" ]; then
            echo "   $line"
        fi
    done
else
    echo "⚠️ Could not retrieve DNS configuration automatically"
    echo "📝 Please check the Cloud Console for DNS records:"
    echo "🔗 https://console.cloud.google.com/run/domains?project=$PROJECT_ID"
fi

echo ""
echo "📝 Manual DNS Configuration:"
echo "1. Go to your DNS provider (e.g., Cloudflare, GoDaddy, etc.)"
echo "2. Add the CNAME record shown above"
echo "3. Point $DOMAIN to the Cloud Run service"
echo ""
echo "🕐 DNS propagation may take up to 24 hours"
echo ""
echo "🧪 Test your domain after DNS propagation:"
echo "🔗 https://$DOMAIN/gov"
echo ""
echo "✅ Domain mapping setup completed!"
echo "📋 Summary:"
echo "   Service: $SERVICE_NAME"
echo "   Domain: $DOMAIN"
echo "   Region: $REGION"
echo "   Project: $PROJECT_ID"
