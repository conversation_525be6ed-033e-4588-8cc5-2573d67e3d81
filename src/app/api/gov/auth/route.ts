import { NextRequest, NextResponse } from 'next/server';
import { validateGovUser, updateGovUserLastLogin, createGovUser } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    const { entity, credential, accessCode } = await request.json();

    // Validate input
    if (!entity || !credential || !accessCode) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      );
    }

    // For demo purposes, create test user if it doesn't exist
    if (entity.toLowerCase() === 'test' && credential === '<EMAIL>' && accessCode === 'brycebayens123') {
      try {
        // Try to validate existing user first
        let user = await validateGovUser(entity, credential, accessCode);
        
        // If user doesn't exist, create them
        if (!user) {
          user = await createGovUser({
            entity: 'test',
            credential: '<EMAIL>',
            accessCode: 'brycebayens123', // In production, this should be hashed
            isActive: true,
          });
        }

        if (user) {
          // Update last login
          await updateGovUserLastLogin(user.id);

          return NextResponse.json({
            success: true,
            user: {
              id: user.id,
              entity: user.entity,
              credential: user.credential,
            },
          });
        }
      } catch (dbError) {
        console.error('Database error:', dbError);
        // Continue to validation error below
      }
    }

    // Validate government user credentials
    const user = await validateGovUser(entity, credential, accessCode);

    if (!user) {
      return NextResponse.json(
        { error: 'Invalid credentials or unauthorized access' },
        { status: 401 }
      );
    }

    // Update last login timestamp
    await updateGovUserLastLogin(user.id);

    // Return success response
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        entity: user.entity,
        credential: user.credential,
      },
    });

  } catch (error) {
    console.error('Government authentication error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle GET requests for health check
export async function GET() {
  return NextResponse.json({
    status: 'Government authentication endpoint active',
    timestamp: new Date().toISOString(),
  });
}
