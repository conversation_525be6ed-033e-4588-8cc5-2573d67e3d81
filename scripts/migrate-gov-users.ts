#!/usr/bin/env tsx

import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import { sql } from 'drizzle-orm';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

async function runMigration() {
  if (!process.env.DATABASE_URL) {
    throw new Error('DATABASE_URL is not set');
  }

  const sqlDb = neon(process.env.DATABASE_URL);
  const db = drizzle(sqlDb);

  try {
    console.log('🚀 Running government users migration...');

    // Execute migration statements individually
    console.log('📝 Creating gov_users table...');
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS gov_users (
          id SERIAL PRIMARY KEY,
          entity VARCHAR(255) NOT NULL,
          credential TEXT NOT NULL UNIQUE,
          access_code TEXT NOT NULL,
          is_active BOOLEAN DEFAULT true NOT NULL,
          last_login TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
      )
    `);

    console.log('📝 Creating indexes...');
    await db.execute(sql`CREATE INDEX IF NOT EXISTS gov_users_entity_idx ON gov_users(entity)`);
    await db.execute(sql`CREATE UNIQUE INDEX IF NOT EXISTS gov_users_credential_key ON gov_users(credential)`);

    console.log('📝 Inserting test data...');
    await db.execute(sql`
      INSERT INTO gov_users (entity, credential, access_code, is_active)
      VALUES ('test', '<EMAIL>', 'brycebayens123', true)
      ON CONFLICT (credential) DO NOTHING
    `);

    console.log('📝 Adding table comment...');
    await db.execute(sql`COMMENT ON TABLE gov_users IS 'Government entity users for FaceTrace Gov portal authentication'`);

    console.log('✅ Government users migration completed successfully!');
    console.log('📝 Test user created:');
    console.log('   Entity: test');
    console.log('   Credential: <EMAIL>');
    console.log('   Access Code: brycebayens123');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runMigration().catch(console.error);
