#!/bin/bash

# DNS Propagation Monitor for facetrace.pro
# This script monitors DNS propagation after nameserver changes

set -e

# Configuration
DOMAIN_NAME="facetrace.pro"
PROJECT_ID="facetracepro"
ZONE_NAME="facetrace-pro-zone"

# Expected Google Cloud nameservers
EXPECTED_NS=(
    "ns-cloud-a1.googledomains.com"
    "ns-cloud-a2.googledomains.com"
    "ns-cloud-a3.googledomains.com"
    "ns-cloud-a4.googledomains.com"
)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "\n${CYAN}🔍 $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check nameservers
check_nameservers() {
    log_header "Checking Nameservers"
    
    if command_exists dig; then
        local current_ns=$(dig +short NS $DOMAIN_NAME 2>/dev/null | sort)
        
        if [[ -n "$current_ns" ]]; then
            echo "📋 Current nameservers:"
            echo "$current_ns" | while read ns; do
                if [[ -n "$ns" ]]; then
                    # Remove trailing dot for comparison
                    ns_clean=${ns%.}
                    echo "   $ns"
                    
                    # Check if it's a Google Cloud nameserver
                    if [[ " ${EXPECTED_NS[@]} " =~ " ${ns_clean} " ]]; then
                        echo -e "     ${GREEN}✓ Google Cloud DNS${NC}"
                    else
                        echo -e "     ${YELLOW}⚠ External DNS provider${NC}"
                    fi
                fi
            done
            
            # Check if all nameservers are Google Cloud
            local google_ns_count=0
            for expected in "${EXPECTED_NS[@]}"; do
                if echo "$current_ns" | grep -q "$expected"; then
                    ((google_ns_count++))
                fi
            done
            
            if [[ $google_ns_count -eq ${#EXPECTED_NS[@]} ]]; then
                log_success "All nameservers are Google Cloud DNS"
                return 0
            else
                log_warning "Not all nameservers are Google Cloud DNS ($google_ns_count/${#EXPECTED_NS[@]})"
                return 1
            fi
        else
            log_error "Could not retrieve nameservers"
            return 1
        fi
    else
        log_warning "dig command not available"
        return 1
    fi
}

# Function to check A records
check_a_records() {
    log_header "Checking A Records"
    
    if command_exists dig; then
        local a_record=$(dig +short A $DOMAIN_NAME 2>/dev/null)
        
        if [[ -n "$a_record" ]]; then
            echo "📋 A record for $DOMAIN_NAME:"
            echo "   $a_record"
            
            # Check if it matches expected IP
            if [[ "$a_record" == "***********" ]]; then
                log_success "A record matches expected IP (Vercel)"
            else
                log_info "A record: $a_record (may be updated)"
            fi
        else
            log_warning "No A record found for $DOMAIN_NAME"
        fi
    else
        log_warning "dig command not available"
    fi
}

# Function to check CNAME records
check_cname_records() {
    log_header "Checking CNAME Records"
    
    local subdomains=("www" "api" "gov")
    
    for subdomain in "${subdomains[@]}"; do
        local full_domain="$subdomain.$DOMAIN_NAME"
        
        if command_exists dig; then
            local cname_record=$(dig +short CNAME $full_domain 2>/dev/null)
            
            if [[ -n "$cname_record" ]]; then
                echo "📋 CNAME for $full_domain:"
                echo "   $cname_record"
                
                case $subdomain in
                    "www")
                        if [[ "$cname_record" == "cname.vercel-dns.com." ]]; then
                            log_success "WWW CNAME correct (Vercel)"
                        else
                            log_info "WWW CNAME: $cname_record"
                        fi
                        ;;
                    "api"|"gov")
                        if [[ "$cname_record" == "ghs.googlehosted.com." ]]; then
                            log_success "$subdomain CNAME ready for Cloud Run"
                        else
                            log_info "$subdomain CNAME: $cname_record"
                        fi
                        ;;
                esac
            else
                log_warning "No CNAME record found for $full_domain"
            fi
        fi
    done
}

# Function to check MX records
check_mx_records() {
    log_header "Checking MX Records"
    
    if command_exists dig; then
        local mx_records=$(dig +short MX $DOMAIN_NAME 2>/dev/null)
        
        if [[ -n "$mx_records" ]]; then
            echo "📋 MX records for $DOMAIN_NAME:"
            echo "$mx_records" | while read mx; do
                if [[ -n "$mx" ]]; then
                    echo "   $mx"
                fi
            done
            
            if echo "$mx_records" | grep -q "aspmx.l.google.com"; then
                log_success "MX records configured for Google Workspace"
            else
                log_info "MX records present (custom configuration)"
            fi
        else
            log_warning "No MX records found"
        fi
    else
        log_warning "dig command not available"
    fi
}

# Function to check TXT records
check_txt_records() {
    log_header "Checking TXT Records"
    
    if command_exists dig; then
        local txt_records=$(dig +short TXT $DOMAIN_NAME 2>/dev/null)
        
        if [[ -n "$txt_records" ]]; then
            echo "📋 TXT records for $DOMAIN_NAME:"
            echo "$txt_records" | while read txt; do
                if [[ -n "$txt" ]]; then
                    echo "   $txt"
                    
                    if [[ "$txt" == *"v=spf1"* ]]; then
                        log_success "SPF record found"
                    elif [[ "$txt" == *"google-site-verification"* ]]; then
                        log_success "Google verification record found"
                    fi
                fi
            done
        else
            log_warning "No TXT records found"
        fi
    else
        log_warning "dig command not available"
    fi
}

# Function to test multiple DNS servers
test_multiple_dns_servers() {
    log_header "Testing Multiple DNS Servers"
    
    local dns_servers=("*******" "*******" "**************" "*******")
    local dns_names=("Google" "Cloudflare" "OpenDNS" "Quad9")
    
    for i in "${!dns_servers[@]}"; do
        local dns_server="${dns_servers[$i]}"
        local dns_name="${dns_names[$i]}"
        
        echo "🔍 Testing $dns_name DNS ($dns_server):"
        
        if command_exists dig; then
            local ns_result=$(dig @$dns_server +short NS $DOMAIN_NAME 2>/dev/null | head -1)
            local a_result=$(dig @$dns_server +short A $DOMAIN_NAME 2>/dev/null)
            
            if [[ -n "$ns_result" ]]; then
                ns_clean=${ns_result%.}
                if [[ " ${EXPECTED_NS[@]} " =~ " ${ns_clean} " ]]; then
                    echo -e "   NS: ${GREEN}✓ Google Cloud${NC}"
                else
                    echo -e "   NS: ${YELLOW}⚠ $ns_result${NC}"
                fi
            else
                echo -e "   NS: ${RED}❌ No response${NC}"
            fi
            
            if [[ -n "$a_result" ]]; then
                echo "   A:  $a_result"
            else
                echo -e "   A:  ${RED}❌ No response${NC}"
            fi
        fi
        echo ""
    done
}

# Function to show Google Cloud DNS status
show_gcloud_status() {
    log_header "Google Cloud DNS Status"
    
    if command_exists gcloud; then
        # Check if authenticated
        if gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
            echo "📋 Google Cloud DNS records:"
            gcloud dns record-sets list --zone=$ZONE_NAME --project=$PROJECT_ID 2>/dev/null || {
                log_warning "Could not retrieve Google Cloud DNS records"
                log_info "Make sure you're authenticated: gcloud auth login"
            }
        else
            log_warning "Not authenticated with Google Cloud"
            log_info "Run: gcloud auth login"
        fi
    else
        log_warning "gcloud CLI not available"
    fi
}

# Function to show propagation status
show_propagation_status() {
    log_header "DNS Propagation Status"
    
    # Check if nameservers have propagated
    if check_nameservers >/dev/null 2>&1; then
        log_success "DNS propagation appears complete"
        echo ""
        echo "✅ Your domain is now using Google Cloud DNS!"
        echo "🚀 You can now:"
        echo "   1. Deploy Cloud Run services"
        echo "   2. Set up domain mappings"
        echo "   3. Configure SSL certificates"
    else
        log_warning "DNS propagation still in progress"
        echo ""
        echo "⏱️  DNS propagation can take 24-48 hours"
        echo "🔄 Check again later or run this script periodically"
        echo ""
        echo "📝 If it's been more than 48 hours:"
        echo "   1. Verify nameservers are correctly set at your registrar"
        echo "   2. Check for any DNS caching issues"
        echo "   3. Contact your registrar if needed"
    fi
}

# Main monitoring function
main() {
    echo "🌐 DNS Propagation Monitor for $DOMAIN_NAME"
    echo "============================================="
    echo "Timestamp: $(date)"
    echo ""
    
    # Check prerequisites
    if ! command_exists dig; then
        log_warning "dig command not available. Install dnsutils for better monitoring."
        echo "   macOS: brew install bind"
        echo "   Ubuntu/Debian: sudo apt-get install dnsutils"
        echo "   CentOS/RHEL: sudo yum install bind-utils"
        echo ""
    fi
    
    # Run all checks
    check_nameservers
    check_a_records
    check_cname_records
    check_mx_records
    check_txt_records
    test_multiple_dns_servers
    show_gcloud_status
    show_propagation_status
    
    echo ""
    echo "🔄 To monitor continuously, run:"
    echo "   watch -n 300 ./monitor-dns-propagation.sh"
    echo ""
    echo "📊 For detailed status, run:"
    echo "   ./domain-status-check.sh"
}

# Handle command line arguments
case "${1:-}" in
    --continuous)
        echo "🔄 Starting continuous monitoring (every 5 minutes)..."
        echo "Press Ctrl+C to stop"
        while true; do
            clear
            main
            sleep 300
        done
        ;;
    --help|-h)
        echo "DNS Propagation Monitor"
        echo ""
        echo "Usage:"
        echo "  $0                 Run single check"
        echo "  $0 --continuous    Run continuous monitoring"
        echo "  $0 --help         Show this help"
        ;;
    *)
        main
        ;;
esac
